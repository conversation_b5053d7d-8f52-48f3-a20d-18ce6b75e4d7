<?php
/**
 * Test Splash Screen Configuration
 * 
 * This file helps test and debug PWA splash screen functionality.
 * Access via: /wp-content/plugins/q-pusher-q-pwa/test-splash-screen.php
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    // Load WordPress if not already loaded
    $wp_load_path = dirname(dirname(dirname(dirname(__FILE__)))) . '/wp-load.php';
    if (file_exists($wp_load_path)) {
        require_once $wp_load_path;
    } else {
        die('WordPress not found');
    }
}

// Include the PWA classes
require_once plugin_dir_path(__FILE__) . 'includes/class-q-pwa-manifest.php';
require_once plugin_dir_path(__FILE__) . 'includes/class-q-pwa-settings.php';

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PWA Splash Screen Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .status-card {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .status-good { border-left: 4px solid #28a745; }
        .status-warning { border-left: 4px solid #ffc107; }
        .status-error { border-left: 4px solid #dc3545; }
        .code-block {
            background: #f1f3f4;
            border: 1px solid #dadce0;
            border-radius: 4px;
            padding: 12px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
        .meta-tag {
            margin: 5px 0;
            padding: 5px;
            background: #e9ecef;
            border-radius: 3px;
            font-size: 12px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #dee2e6;
            padding: 8px 12px;
            text-align: left;
        }
        th {
            background: #f8f9fa;
            font-weight: 600;
        }
        .btn {
            display: inline-block;
            padding: 8px 16px;
            background: #007cba;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            margin: 5px;
        }
        .btn:hover {
            background: #005a87;
        }
    </style>
</head>
<body>
    <h1>PWA Splash Screen Test & Diagnostics</h1>
    
    <?php
    // Get splash screen diagnostics
    $diagnostics = Q_PWA_Manifest::get_splash_screen_diagnostics();
    $manifest_validation = Q_PWA_Manifest::validate_manifest();
    ?>
    
    <div class="status-card <?php echo $diagnostics['valid'] ? 'status-good' : 'status-error'; ?>">
        <h2>Overall Status: <?php echo $diagnostics['valid'] ? '✅ Good' : '❌ Issues Found'; ?></h2>
        <p>PWA Enabled: <?php echo get_option('q_pwa_enabled', false) ? '✅ Yes' : '❌ No'; ?></p>
        <p>Generated Splash Screens: <?php echo $diagnostics['diagnostics']['generated_splash_screens']; ?></p>
        <p>iOS Splash Screens: <?php echo $diagnostics['diagnostics']['ios_splash_screens']; ?></p>
        <p>Cached Images: <?php echo $diagnostics['diagnostics']['cache_info']['cached_images']; ?></p>
    </div>

    <?php if (!empty($diagnostics['errors'])): ?>
    <div class="status-card status-error">
        <h3>❌ Errors</h3>
        <ul>
            <?php foreach ($diagnostics['errors'] as $error): ?>
                <li><?php echo esc_html($error); ?></li>
            <?php endforeach; ?>
        </ul>
    </div>
    <?php endif; ?>

    <?php if (!empty($diagnostics['warnings'])): ?>
    <div class="status-card status-warning">
        <h3>⚠️ Warnings</h3>
        <ul>
            <?php foreach ($diagnostics['warnings'] as $warning): ?>
                <li><?php echo esc_html($warning); ?></li>
            <?php endforeach; ?>
        </ul>
    </div>
    <?php endif; ?>

    <div class="status-card">
        <h3>Configuration</h3>
        <table>
            <tr>
                <th>Setting</th>
                <th>Value</th>
                <th>Status</th>
            </tr>
            <tr>
                <td>Splash Screen Image</td>
                <td><?php echo esc_html($diagnostics['diagnostics']['configuration']['splash_image'] ?: 'Not set'); ?></td>
                <td><?php echo $diagnostics['diagnostics']['configuration']['splash_image'] ? '✅' : '❌'; ?></td>
            </tr>
            <tr>
                <td>512x512 Icon</td>
                <td><?php echo esc_html($diagnostics['diagnostics']['configuration']['icon_512'] ?: 'Not set'); ?></td>
                <td><?php echo $diagnostics['diagnostics']['configuration']['icon_512'] ? '✅' : '❌'; ?></td>
            </tr>
            <tr>
                <td>Main Icon</td>
                <td><?php echo esc_html($diagnostics['diagnostics']['configuration']['main_icon'] ?: 'Not set'); ?></td>
                <td><?php echo $diagnostics['diagnostics']['configuration']['main_icon'] ? '✅' : '❌'; ?></td>
            </tr>
            <tr>
                <td>Background Color</td>
                <td>
                    <span style="display: inline-block; width: 20px; height: 20px; background: <?php echo esc_attr($diagnostics['diagnostics']['configuration']['background_color']); ?>; border: 1px solid #ccc; margin-right: 8px;"></span>
                    <?php echo esc_html($diagnostics['diagnostics']['configuration']['background_color']); ?>
                </td>
                <td>✅</td>
            </tr>
            <tr>
                <td>iOS Web App Capable</td>
                <td><?php echo $diagnostics['diagnostics']['configuration']['ios_capable'] ? 'Yes' : 'No'; ?></td>
                <td><?php echo $diagnostics['diagnostics']['configuration']['ios_capable'] ? '✅' : '⚠️'; ?></td>
            </tr>
        </table>
    </div>

    <div class="status-card">
        <h3>Generated iOS Splash Screen Meta Tags (Sample)</h3>
        <p>The following meta tags would be generated for iOS devices:</p>
        <div class="code-block">
<?php
// Capture the iOS splash screen meta tags
ob_start();
if (class_exists('Q_PWA_Settings')) {
    Q_PWA_Settings::add_ios_splash_screen_meta_tags();
}
$ios_meta_tags = ob_get_clean();

if ($ios_meta_tags) {
    echo esc_html($ios_meta_tags);
} else {
    echo "No iOS splash screen meta tags generated. Check configuration above.";
}
?>
        </div>
    </div>

    <div class="status-card">
        <h3>PWA Manifest Validation</h3>
        <?php if ($manifest_validation['valid']): ?>
            <p>✅ Manifest is valid</p>
        <?php else: ?>
            <p>❌ Manifest has issues:</p>
            <ul>
                <?php foreach ($manifest_validation['errors'] as $error): ?>
                    <li><?php echo esc_html($error); ?></li>
                <?php endforeach; ?>
            </ul>
        <?php endif; ?>
        
        <p><strong>Manifest URL:</strong> <a href="<?php echo esc_url(home_url('/manifest.json')); ?>" target="_blank"><?php echo esc_url(home_url('/manifest.json')); ?></a></p>
    </div>

    <div class="status-card">
        <h3>Testing Instructions</h3>
        <ol>
            <li><strong>iOS Testing:</strong>
                <ul>
                    <li>Open your site in Safari on iOS</li>
                    <li>Tap the Share button and select "Add to Home Screen"</li>
                    <li>Install the PWA and launch it from the home screen</li>
                    <li>The splash screen should display with your configured image and background color</li>
                </ul>
            </li>
            <li><strong>Android Testing:</strong>
                <ul>
                    <li>Open your site in Chrome on Android</li>
                    <li>Look for the "Add to Home Screen" prompt or use the menu option</li>
                    <li>Install the PWA and launch it</li>
                    <li>The splash screen should display based on the manifest configuration</li>
                </ul>
            </li>
            <li><strong>Desktop Testing:</strong>
                <ul>
                    <li>Open your site in Chrome or Edge</li>
                    <li>Look for the install prompt in the address bar</li>
                    <li>Install the PWA and launch it</li>
                </ul>
            </li>
        </ol>
    </div>

    <div class="status-card">
        <h3>Actions</h3>
        <a href="<?php echo admin_url('admin.php?page=q-pwa-settings'); ?>" class="btn">Configure PWA Settings</a>
        <a href="<?php echo esc_url(home_url('/manifest.json')); ?>" class="btn" target="_blank">View Manifest</a>
        <a href="<?php echo admin_url('admin.php?page=q-pwa-settings&tab=images'); ?>" class="btn">Upload Splash Screen</a>
    </div>

    <div class="status-card">
        <h3>Troubleshooting</h3>
        <ul>
            <li><strong>Splash screen not showing:</strong> Ensure PWA is enabled and you have uploaded at least one icon</li>
            <li><strong>Wrong colors:</strong> Check the background color setting in PWA configuration</li>
            <li><strong>Blurry images:</strong> Upload higher resolution images (at least 512x512 for icons, 2048x2732 for splash screens)</li>
            <li><strong>iOS issues:</strong> Make sure "iOS Web App Capable" is enabled in iOS settings</li>
            <li><strong>Cache issues:</strong> Clear browser cache and regenerate manifest</li>
        </ul>
    </div>

    <footer style="margin-top: 40px; padding-top: 20px; border-top: 1px solid #dee2e6; color: #6c757d; font-size: 14px;">
        <p>Q-PWA Splash Screen Test - Generated at <?php echo date('Y-m-d H:i:s'); ?></p>
    </footer>
</body>
</html>
