<?php
/**
 * Fix PWA Install Button for Guest Users
 * 
 * This file contains fixes to ensure PWA install buttons work for non-logged-in users
 * Include this file in your functions.php or as a separate plugin
 */

// Ensure PWA scripts are properly loaded for all users
add_action('wp_enqueue_scripts', 'fix_pwa_scripts_for_guests', 20);

function fix_pwa_scripts_for_guests() {
    // Only run on frontend
    if (is_admin()) {
        return;
    }

    // Check if PWA is enabled
    if (!get_option('q_pwa_enabled', false)) {
        return;
    }

    // Ensure PWA manager script is enqueued
    if (!wp_script_is('q-pwa-manager', 'enqueued')) {
        wp_enqueue_script(
            'q-pwa-manager',
            plugins_url('includes/js/pwa-manager.js', dirname(__FILE__) . '/q-pusher.php'),
            array('jquery'),
            filemtime(plugin_dir_path(dirname(__FILE__)) . 'includes/js/pwa-manager.js'),
            true
        );
    }

    // Ensure qPWASettings is localized for all users
    $icon_url = get_option('q_pwa_icon', '');
    if ($icon_url && !preg_match('/^https?:\/\//', $icon_url)) {
        $icon_url = site_url($icon_url);
    }

    wp_localize_script('q-pwa-manager', 'qPWASettings', [
        'enabled' => (bool) get_option('q_pwa_enabled', false),
        'appName' => get_option('q_pwa_app_name', get_bloginfo('name')),
        'themeColor' => get_option('q_pwa_theme_color', '#000000'),
        'backgroundColor' => get_option('q_pwa_background_color', '#ffffff'),
        'cacheVersion' => get_option('q_pwa_cache_version', 'v1'),
        'cacheTTL' => 3600 * 24 * 7, // 1 week default
        'siteUrl' => home_url('/'),
        'ajaxUrl' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('q_pwa_nonce'),
        'windowControlsOverlay' => (bool) get_option('q_pwa_window_controls_overlay', false),
        'wcoCaptionButtonClose' => '',
        'analyticsEnabled' => (bool) get_option('q_pwa_analytics_enabled', true),
        'iconUrl' => $icon_url,
        'appVersion' => '1.0.0', // Fallback version
        'currentUserId' => get_current_user_id(), // 0 for guests, which is fine
        'installableOnDesktop' => (bool) get_option('q_pwa_installable_on_desktop', false)
    ]);
}

// Add inline script to ensure PWA manager initializes properly
add_action('wp_footer', 'ensure_pwa_manager_init', 5);

function ensure_pwa_manager_init() {
    // Only run on frontend
    if (is_admin()) {
        return;
    }

    // Check if PWA is enabled
    if (!get_option('q_pwa_enabled', false)) {
        return;
    }

    ?>
    <script>
    // Ensure PWA Manager initializes for all users
    (function() {
        'use strict';
        
        function initPWAForAllUsers() {
            // Check if qPWASettings exists
            if (typeof window.qPWASettings === 'undefined') {
                console.warn('Q-PWA: qPWASettings not found, PWA functionality may not work');
                return;
            }

            // Initialize QPWAManager if not already initialized
            if (typeof window.qPWAManager === 'undefined' && typeof window.QPWAManager !== 'undefined') {
                if (window.qPWASettings.enabled) {
                    console.log('Q-PWA: Initializing PWA Manager for guest user');
                    window.qPWAManager = new window.QPWAManager();
                }
            }

            // Ensure install buttons are initialized
            if (typeof window.qPWAManager !== 'undefined') {
                initInstallButtonsForGuests();
            }
        }

        function initInstallButtonsForGuests() {
            const installButtons = document.querySelectorAll('.q-pwa-install-button');
            
            installButtons.forEach((button) => {
                // Set up click handler if not already set
                if (!button.hasAttribute('data-pwa-initialized')) {
                    button.addEventListener('click', function() {
                        if (window.qPWAManager && window.qPWAManager.installApp) {
                            window.qPWAManager.installApp();
                        } else {
                            console.warn('Q-PWA: PWA Manager not available for installation');
                        }
                    });
                    button.setAttribute('data-pwa-initialized', 'true');
                }

                // Update button visibility
                updateInstallButtonVisibilityForGuests(button);
            });

            // Listen for install state changes
            window.addEventListener('beforeinstallprompt', () => {
                installButtons.forEach(updateInstallButtonVisibilityForGuests);
            });

            window.addEventListener('appinstalled', () => {
                installButtons.forEach(updateInstallButtonVisibilityForGuests);
            });
        }

        function updateInstallButtonVisibilityForGuests(button) {
            if (!button) return;

            const showWhenInstalled = button.getAttribute('data-show-when-installed') === 'true';
            const isInstalled = window.matchMedia('(display-mode: standalone)').matches || 
                               window.navigator.standalone === true;
            const hasInstallPrompt = window.qPWAManager && window.qPWAManager.deferredPrompt;

            // Show button if:
            // 1. PWA is not installed and we have an install prompt, OR
            // 2. PWA is installed and show_when_installed is true, OR
            // 3. Force show for testing (show_when_installed is true)
            const shouldShow = (!isInstalled && hasInstallPrompt) || 
                              (isInstalled && showWhenInstalled) ||
                              showWhenInstalled; // This allows testing

            button.style.display = shouldShow ? 'inline-block' : 'none';
        }

        // Initialize when DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initPWAForAllUsers);
        } else {
            initPWAForAllUsers();
        }

        // Also try after a short delay to catch any late-loading elements
        setTimeout(initPWAForAllUsers, 1000);
    })();
    </script>
    <?php
}

// Add CSS to ensure buttons are visible when they should be
add_action('wp_head', 'pwa_button_guest_css');

function pwa_button_guest_css() {
    if (is_admin()) {
        return;
    }
    ?>
    <style>
    /* Ensure PWA install buttons are properly styled for all users */
    .q-pwa-install-button {
        cursor: pointer;
        text-decoration: none;
        display: inline-block;
        transition: opacity 0.3s ease;
    }
    
    .q-pwa-install-button:hover {
        opacity: 0.8;
    }
    
    /* Default styling if no custom class is provided */
    .q-pwa-install-button.ui-button {
        background: #0073aa;
        color: white;
        border: none;
        padding: 10px 15px;
        border-radius: 3px;
    }
    
    .q-pwa-install-button.ui-button.primary {
        background: #0073aa;
    }
    </style>
    <?php
}

// Debug function to check PWA status for all users
function debug_pwa_status_for_guests() {
    if (!current_user_can('manage_options')) {
        return;
    }
    
    echo '<div style="position: fixed; bottom: 10px; right: 10px; background: black; color: white; padding: 10px; z-index: 9999; font-size: 12px; border-radius: 5px;">';
    echo '<strong>PWA Debug:</strong><br>';
    echo 'PWA Enabled: ' . (get_option('q_pwa_enabled', false) ? 'YES' : 'NO') . '<br>';
    echo 'Desktop Install: ' . (get_option('q_pwa_installable_on_desktop', false) ? 'YES' : 'NO') . '<br>';
    echo 'User ID: ' . get_current_user_id() . '<br>';
    echo 'HTTPS: ' . (is_ssl() ? 'YES' : 'NO') . '<br>';
    echo '</div>';
}

// Uncomment the line below to enable debug info for admins
// add_action('wp_footer', 'debug_pwa_status_for_guests');
?>
